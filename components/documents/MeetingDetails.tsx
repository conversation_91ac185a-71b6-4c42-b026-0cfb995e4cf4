import React, { useState } from 'react';
import { format } from 'date-fns';
import { Users, X, CalendarIcon, Plus } from 'lucide-react';
import AttendeeAssignmentPopover from './AttendeeAssignmentPopover';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id, Doc } from '@/convex/_generated/dataModel'; // Import Doc
import { useToast } from '@/components/hooks/use-toast';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import Link from 'next/link'; // Import Link
import { Badge } from '@/components/ui/badge'; // Import Badge
import { cn } from '@/lib/utils'; // Import cn utility

interface MeetingDetailsProps {
  taggableId: Id<'files'>;
  meetingDate?: number | null;
  attendeeIds?: (Id<'people'> | Id<'organizations'>)[] | null;
}

const MeetingDetails: React.FC<MeetingDetailsProps> = ({ 
  taggableId, 
  meetingDate, 
  attendeeIds = [] 
}) => {
  const { toast } = useToast();
  const [isUpdating, setIsUpdating] = useState(false);
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(
    meetingDate ? new Date(meetingDate) : new Date()
  );

  // Fetch attendee details - Define a type for the expected return value
  type AttendeeDetail = {
    id: Id<string>;
    type: 'person' | 'organization';
    name: string;
    image?: string | null;
    initials: string;
    email?: string | null;
    organizations?: { id: Id<string>; name: string }[];
  };
  const attendees = useQuery(api.files.fileStorageQueries.getAttendeeDetails, { 
    attendeeIds: attendeeIds?.map(id => id.toString()) || []
  }) || [];

  // Update meeting note mutation
  const updateMeetingNote = useMutation(api.files.meetingNoteMutations.updateMeetingNote);

  // Handle date change
  const handleDateChange = async (date: Date | undefined) => {
    if (!date) return;
    
    setSelectedDate(date);
    setIsUpdating(true);
    
    try {
      await updateMeetingNote({
        fileId: taggableId,
        meetingDate: date.getTime()
      });
      
      toast({
        title: "Meeting date updated",
        description: `Meeting date set to ${format(date, 'PPP')}`
      });
    } catch (error) {
      toast({
        title: "Error updating meeting date",
        description: error instanceof Error ? error.message : "Failed to update meeting date",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Handle removing an attendee
  const handleRemoveAttendee = async (attendeeId: Id<any>) => {
    if (!attendeeIds) return;
    
    setIsUpdating(true);
    
    try {
      const newAttendees = attendeeIds.filter(id => id !== attendeeId);
      
      await updateMeetingNote({
        fileId: taggableId,
        attendees: newAttendees
      });
      
      toast({
        title: "Attendee removed",
        description: "Attendee has been removed from the meeting"
      });
    } catch (error) {
      toast({
        title: "Error removing attendee",
        description: error instanceof Error ? error.message : "Failed to remove attendee",
        variant: "destructive"
      });
    } finally {
      setIsUpdating(false);
    }
  };

  // Format the date for display
  const formattedDate = selectedDate 
    ? format(selectedDate, 'MMMM d, yyyy')
    : 'Not set';

  return (
    <Card>
      <CardHeader className="px-4 py-3">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-muted-foreground" />
          Meeting Details
        </CardTitle>
      </CardHeader>
      <CardContent className="px-4 pb-4 pt-0 space-y-4">
        {/* Meeting Date Section */}
        <div>
          <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-2">Date</h4>
          <Popover>
            <PopoverTrigger asChild>
              <Button 
                variant="ghost" 
                className="w-full justify-start text-left font-normal"
                disabled={isUpdating}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {formattedDate}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={handleDateChange}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>

        {/* Attendees Section */}
        <div>
          <h4 className="text-xs font-semibold uppercase text-muted-foreground mb-2 flex items-center justify-between">
            <span className="flex items-center gap-1">
              <Users className="h-3.5 w-3.5" />
              Attendees
            </span>
            <span className="text-xs font-normal">({attendees?.length || 0})</span>
          </h4>
          
          {/* Attendee List - Single Line Layout with Links and Chips */}
          <div className="space-y-1 max-h-[200px] overflow-y-auto pr-1">
            {attendees.length > 0 ? (
              attendees.map((attendee) => {
                const attendeeUrl = attendee.type === 'person'
                  ? `/directory/people/${attendee.id}`
                  : `/directory/organizations/${attendee.id}`;
                  
                return (
                  <div 
                    key={attendee.id.toString()} 
                    className="flex items-center justify-between rounded-sm border p-1.5 group hover:bg-muted/50 transition-colors"
                  >
                    {/* Link wrapping the main content */}
                    <Link href={attendeeUrl} className="flex items-center gap-1.5 overflow-hidden flex-grow cursor-pointer">
                      {/* Icon/Avatar */}
                      <div className="flex-shrink-0">
                        {attendee.type === 'person' ? (
                          <Avatar className="h-5 w-5 rounded-sm">
                            <AvatarImage src={attendee.image ?? undefined} alt={attendee.name} />
                            <AvatarFallback className="text-xs rounded-sm">{attendee.initials}</AvatarFallback>
                          </Avatar>
                        ) : (
                          <div className="flex items-center justify-center h-5 w-5 bg-muted rounded-sm">
                            <Users className="h-3 w-3 text-muted-foreground" />
                          </div>
                        )}
                      </div>
                      {/* Name and Org Chips (Inline) */}
                      <div className="flex items-center gap-1.5 min-w-0">
                        <span className="text-sm font-normal flex-shrink-0">{attendee.name}</span>
                        {/* Organization Chips for People */}
                        {attendee.type === 'person' && attendee.organizations && attendee.organizations.length > 0 && (
                          <div className="flex items-center gap-1 flex-shrink overflow-hidden">
                            {attendee.organizations.slice(0, 1).map(org => (
                              <Badge key={org.id} variant="outline" className="text-xs px-1.5 py-0 h-4 font-normal border rounded-sm whitespace-nowrap overflow-hidden text-ellipsis">
                                {org.name}
                              </Badge>
                            ))}
                            {attendee.organizations.length > 1 && (
                              <Badge variant="outline" className="text-xs px-1.5 py-0 h-4 font-normal border flex-shrink-0 rounded-sm whitespace-nowrap">
                                +{attendee.organizations.length - 1}
                              </Badge>
                            )}
                          </div>
                        )}
                      </div>
                    </Link>
                    
                    {/* Remove Button */}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-5 w-5 opacity-0 group-hover:opacity-100 transition-opacity flex-shrink-0 ml-1" // Added margin
                      onClick={(e) => {
                        e.stopPropagation(); // Prevent link navigation
                        handleRemoveAttendee(attendee.id);
                      }}
                      disabled={isUpdating}
                      aria-label={`Remove ${attendee.name}`}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </div>
                );
              })
            ) : (
              <div className="text-sm text-muted-foreground text-center py-2">
                No attendees added yet
              </div>
            )}
          </div>
          
          {/* Add Attendee Button with Popover */}
          <AttendeeAssignmentPopover
            taggableId={taggableId}
            existingAttendeeIds={attendeeIds || []}
            triggerButton={
              <Button
                variant="ghost"
                size="sm"
                className="w-full h-7 mt-2 justify-center items-center border-2 border-dashed border-muted-foreground/30 text-muted-foreground hover:bg-muted/50 hover:text-foreground text-xs"
                disabled={isUpdating}
              >
                <Plus className="h-3.5 w-3.5 mr-1" />
                Add Attendee
              </Button>
            }
          />
        </div>
      </CardContent>
    </Card>
  );
};

export default MeetingDetails;
