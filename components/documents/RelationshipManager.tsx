'use client';

import React, { useState, useMemo } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger, DialogFooter, DialogClose } from '@/components/ui/dialog';
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { X, Link as LinkIcon } from 'lucide-react'; 
// Import the specific RelationshipLink type from Zod schema
import type { RelationshipLink } from '@/zod/files-schema'; 

// Define the structure for search results
type SearchResult = {
  _id: Id<'tasks' | 'projects' | 'decisions' | 'people' | 'organizations' | 'teams'>;
  title?: string;
  name?: string;
  type: 'task' | 'project' | 'decision' | 'user' | 'team' | 'organization';
};

interface RelationshipManagerProps {
  fileId: Id<'files'>;
  // Update callback to use the correct RelationshipLink type
  onRelationshipsChange: (added: RelationshipLink[], removed: RelationshipLink[]) => void; 
}

// Define the structure for related entities returned by the getRelatedEntities query
// This might differ slightly from RelationshipLink if the query transforms data
type DisplayEntity = {
  _id: Id<any>;
  type: string;
  name: string;
};


const RelationshipManager = ({ fileId, onRelationshipsChange }: RelationshipManagerProps) => {
  // Fetch existing relationships - Note: Query returns DisplayEntity structure
  const relatedEntities: DisplayEntity[] | undefined = useQuery(api.files.clientQueries.getRelatedEntities, { fileId });

  // State for the linking dialog
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [selectedTab, setSelectedTab] = useState<string>('tasks'); // Default search tab
  const [searchTerm, setSearchTerm] = useState('');
  const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
  const [isLoadingSearch, setIsLoadingSearch] = useState(false);

  // --- Search Queries ---
  // We use useQuery but disable it initially, enabling only when searching
  const taskResults = useQuery(api.tasks.searchTasksByName, 
    !isLoadingSearch || selectedTab !== 'tasks' || !searchTerm ? 'skip' : { searchQuery: searchTerm, limit: 10 }
  );
  const projectResults = useQuery(api.projects.listProjects, 
    !isLoadingSearch || selectedTab !== 'projects' || !searchTerm ? 'skip' : { 
      filter: { name: searchTerm },
      paginationOpts: { numItems: 10, cursor: null }
    }
  );
  const decisionResults = useQuery(api.decisions.searchDecisionsByTitle, 
    !isLoadingSearch || selectedTab !== 'decisions' || !searchTerm ? 'skip' : { searchQuery: searchTerm, limit: 10 }
  );
  const peopleResults = useQuery(api.directory.directory.searchPeopleAndTeams, 
    !isLoadingSearch || selectedTab !== 'people' || !searchTerm ? 'skip' : { search: searchTerm, limit: 10 }
  );
  const organizationResults = useQuery(api.directory.directoryOrganizations.searchOrganizations, 
    !isLoadingSearch || selectedTab !== 'organizations' || !searchTerm ? 'skip' : { query: searchTerm }
  );
  // TODO: Add other entity types (bills, files, tags?) if needed

  // Effect to update search results based on the active tab and query results
  React.useEffect(() => {
    setIsLoadingSearch(true); // Indicate loading when tab or term changes
    let results: SearchResult[] = [];
    
    // Handle different return types for search queries
    switch (selectedTab) {
      case 'tasks': 
        results = taskResults?.map(task => ({
          _id: task._id as Id<'tasks'>,
          title: task.name,
          type: 'task'
        })) ?? []; 
        break;
      case 'projects': 
        results = projectResults?.page?.map(project => ({
          _id: project._id as Id<'projects'>,
          title: project.name ?? 'Untitled Project',
          type: 'project'
        })) ?? []; 
        break;
      case 'decisions': 
        results = decisionResults?.map(decision => ({
          _id: decision._id as Id<'decisions'>,
          title: decision.name ?? 'Untitled Decision',
          type: 'decision'
        })) ?? []; 
        break;
      case 'people': 
        results = peopleResults?.map(person => ({
          _id: person._id as Id<'people' | 'teams'>,
          name: person.name,
          type: person.type === "person" ? "user" : person.type
        })) ?? [];
        break;
      case 'organizations': 
        results = organizationResults?.map(org => ({
          _id: org._id as Id<'organizations'>,
          name: org.name,
          type: 'organization'
        })) ?? [];
        break;
    }
    setSearchResults(results);
    setIsLoadingSearch(false);
  }, [selectedTab, taskResults, projectResults, decisionResults, peopleResults, organizationResults]);

  // Memoize displayed entities to avoid re-filtering on every render
  const displayedEntities = useMemo(() => relatedEntities ?? [], [relatedEntities]);

  const handleLinkItem = (item: SearchResult) => {
    // Check if already linked
    if (displayedEntities.some(e => e._id === item._id)) {
      // Maybe show a toast?
      console.log("Item already linked");
      return;
    }

    let newItem: RelationshipLink | null = null;

    // Use a switch to create the correctly typed object for the discriminated union
    switch (selectedTab) {
      case 'tasks':
        newItem = { subject_type: 'task', subject_id: item._id as Id<'tasks'> };
        break;
      case 'projects':
        newItem = { subject_type: 'project', subject_id: item._id as Id<'projects'> };
        break;
      case 'decisions':
        newItem = { subject_type: 'decision', subject_id: item._id as Id<'decisions'> };
        break;
      case 'people':
         // Assuming item._id for people is Id<'people'>
        newItem = { subject_type: 'people', subject_id: item._id as Id<'people'> };
        break;
      case 'organizations':
        newItem = { subject_type: 'organization', subject_id: item._id as Id<'organizations'> };
        break;
      // Add cases for other linkable types (bills, files, tags, users?)
      default:
        console.error(`Unhandled entity type for linking: ${selectedTab}`);
        return; // Don't proceed if type is unknown
    }

    if (newItem) {
      // Inform parent: add this item, remove none
      onRelationshipsChange([newItem], []); 
      setIsDialogOpen(false); 
      setSearchTerm(''); 
    }
  };

  const handleUnlinkItem = (itemToUnlink: DisplayEntity) => {
     let itemToRemove: RelationshipLink | null = null;

     // Use a switch to create the correctly typed object for removal
     switch (itemToUnlink.type) {
       case 'tasks': // Assuming itemToUnlink.type matches the literal used in RelationshipLink
         itemToRemove = { subject_type: 'task', subject_id: itemToUnlink._id as Id<'tasks'> };
         break;
       case 'projects':
         itemToRemove = { subject_type: 'project', subject_id: itemToUnlink._id as Id<'projects'> };
         break;
       case 'decisions':
         itemToRemove = { subject_type: 'decision', subject_id: itemToUnlink._id as Id<'decisions'> };
         break;
       case 'people':
         itemToRemove = { subject_type: 'people', subject_id: itemToUnlink._id as Id<'people'> };
         break;
       case 'organizations':
         itemToRemove = { subject_type: 'organization', subject_id: itemToUnlink._id as Id<'organizations'> };
         break;
       // Add cases for other linkable types
       default:
         console.error(`Unhandled entity type for unlinking: ${itemToUnlink.type}`);
         return; // Don't proceed if type is unknown
     }

     if (itemToRemove) {
       // Inform parent: add none, remove this item
       onRelationshipsChange([], [itemToRemove]);
     }
  };

  const getEntityTypeDisplayName = (type: string): string => {
    switch (type) {
      case 'tasks': return 'Task';
      case 'projects': return 'Project';
      case 'decisions': return 'Decision';
      case 'people': return 'Person';
      case 'organizations': return 'Organization';
      // Add more cases as needed
      default: return type.charAt(0).toUpperCase() + type.slice(1);
    }
  };

  return (
    <div className="mt-6 border p-4 rounded bg-muted/40">
      <h3 className="text-lg font-semibold mb-3">Related Items</h3>
      <div className="flex flex-wrap gap-2 mb-3">
        {displayedEntities.length === 0 && <p className="text-sm text-muted-foreground">No items linked yet.</p>}
        {displayedEntities.map((entity) => (
          <Badge key={entity._id} variant="secondary" className="flex items-center gap-1">
            <LinkIcon size={12} className="text-muted-foreground" />
            {/* TODO: Make this a link to the actual entity page */}
            <span className="font-medium">{getEntityTypeDisplayName(entity.type)}:</span> 
            <span>{entity.name}</span>
            <button 
              onClick={() => handleUnlinkItem(entity)} 
              className="ml-1 text-muted-foreground hover:text-destructive"
              aria-label={`Unlink ${entity.name}`}
            >
              <X size={12} />
            </button>
          </Badge>
        ))}
      </div>

      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button variant="outline" size="sm">
            + Link Item
          </Button>
        </DialogTrigger>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Link Item to Article</DialogTitle>
          </DialogHeader>
          <Tabs value={selectedTab} onValueChange={setSelectedTab} className="mt-4">
            <TabsList>
              <TabsTrigger value="tasks">Tasks</TabsTrigger>
              <TabsTrigger value="projects">Projects</TabsTrigger>
              <TabsTrigger value="decisions">Decisions</TabsTrigger>
              <TabsTrigger value="people">People</TabsTrigger>
              <TabsTrigger value="organizations">Orgs</TabsTrigger>
              {/* Add more tabs */}
            </TabsList>
            <Input
              placeholder={`Search ${getEntityTypeDisplayName(selectedTab)}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="my-4"
            />
            <ScrollArea className="h-[250px] border rounded p-2">
              {isLoadingSearch && <p className="text-sm text-muted-foreground">Searching...</p>}
              {!isLoadingSearch && searchResults.length === 0 && searchTerm && (
                <p className="text-sm text-muted-foreground">No results found.</p>
              )}
              {!isLoadingSearch && searchResults.map((item) => (
                <div 
                  key={item._id} 
                  className="p-2 hover:bg-muted rounded cursor-pointer flex justify-between items-center"
                  onClick={() => handleLinkItem(item)}
                >
                  <span>{item.title ?? item.name ?? 'Unnamed'}</span>
                  {/* Optional: Add icon based on type */}
                </div>
              ))}
            </ScrollArea>
          </Tabs>
          <DialogFooter>
            <DialogClose asChild>
              <Button type="button" variant="secondary">
                Cancel
              </Button>
            </DialogClose>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default RelationshipManager;
