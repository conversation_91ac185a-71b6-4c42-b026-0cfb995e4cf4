'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { usePara<PERSON>, useRouter } from 'next/navigation'; 
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/hooks/use-toast';
// Import the existing TiptapEditor component
import { TiptapEditor } from '@/components/tiptap/TiptapEditor'; 
// Import the actual RelationshipManager component
import RelationshipManager from '@/components/documents/RelationshipManager'; 
// Import type for relationship links from Zod schema
import type { RelationshipLink } from '@/zod/files-schema'; 
// Import the DocumentSummaryDisplay component
import DocumentSummaryDisplay from '@/components/documents/DocumentSummaryDisplay';

// No longer need the placeholder
// const RelationshipManagerPlaceholder = ({ fileId }: { fileId: Id<'files'> }) => { ... };

const KnowledgeBaseArticlePage = () => {
  const params = useParams();
  const router = useRouter(); 
  const { toast } = useToast();
  const fileId = params.id as Id<'files'>;

  // State for editable fields
  const [title, setTitle] = useState('');
  // State to hold the latest editor content (HTML string)
  const [currentEditorContent, setCurrentEditorContent] = useState<string>('');
  // State to track if data has been loaded initially
  const [isLoaded, setIsLoaded] = useState(false);
  const [initialTitle, setInitialTitle] = useState<string | null>(null); // Store initial title
  // State for managing relationship changes
  const [relationshipsToAdd, setRelationshipsToAdd] = useState<RelationshipLink[]>([]);
  const [relationshipsToRemove, setRelationshipsToRemove] = useState<RelationshipLink[]>([]);

  // Fetch article data
  const articleData = useQuery(api.files.kbArticleQueries.getKnowledgeBaseArticle, { fileId });
  // Mutation hooks
  const updateArticle = useMutation(api.files.kbArticleMutations.updateKnowledgeBaseArticle);
  const deleteArticleMutation = useMutation(api.files.kbArticleMutations.deleteKnowledgeBaseArticle);

  // Effect to load fetched data into state ONCE
  useEffect(() => {
    // Only set state if data is loaded and hasn't been set before
    if (articleData && !isLoaded) {
      const loadedTitle = articleData.title ?? '';
      setTitle(loadedTitle);
      if (initialTitle === null) { // Set initial title only once
        setInitialTitle(loadedTitle);
      }
      // Ensure kbContent is treated as string | null | undefined before coalescing
      const contentString = articleData.kbContent as string | null | undefined;
      setCurrentEditorContent(contentString ?? '');
      setIsLoaded(true); // Mark as loaded
    }
    // Dependency array includes articleData to trigger when data arrives,
    // and isLoaded to prevent resetting state on subsequent renders.
  }, [articleData, isLoaded, initialTitle]); // Added initialTitle dependency

  // Callback for TiptapEditor's onChange prop
  const handleContentChange = useCallback((newContent: string) => {
    setCurrentEditorContent(newContent);
  }, []);

  // Callback passed to RelationshipManager to handle changes
  const handleRelationshipsChange = useCallback((added: RelationshipLink[], removed: RelationshipLink[]) => {
    // Update state based on incoming changes
    // Add new items to add list, ensuring no duplicates
    setRelationshipsToAdd(prev => [
      ...prev.filter(existing => !added.some(a => a.subject_id === existing.subject_id && a.subject_type === existing.subject_type)), 
      ...added.filter(a => !prev.some(existing => a.subject_id === existing.subject_id && a.subject_type === existing.subject_type))
    ]);
    // Remove items from add list if they are now being removed
    setRelationshipsToAdd(prev => prev.filter(existing => !removed.some(r => r.subject_id === existing.subject_id && r.subject_type === existing.subject_type)));

    // Add new items to remove list, ensuring no duplicates
    setRelationshipsToRemove(prev => [
      ...prev.filter(existing => !removed.some(r => r.subject_id === existing.subject_id && r.subject_type === existing.subject_type)),
      ...removed.filter(r => !prev.some(existing => r.subject_id === existing.subject_id && r.subject_type === existing.subject_type))
    ]);
     // Remove items from remove list if they are now being added back
    setRelationshipsToRemove(prev => prev.filter(existing => !added.some(a => a.subject_id === existing.subject_id && a.subject_type === existing.subject_type)));
  }, []);


  // Callback for TiptapEditor's onSave prop (debounced internally)
  const handleSave = useCallback(async (contentToSave: string) => {
    // Ensure we don't save empty title if it was initially null/undefined
    const titleToSave = title || "Untitled Article"; 
    try {
      await updateArticle({
        input: { // Wrap arguments in 'input' object
          fileId: fileId,
          title: titleToSave, 
          content: contentToSave, 
          // Pass relationship changes
          relationshipsToAdd: relationshipsToAdd,
          relationshipsToRemove: relationshipsToRemove,
        } // Added missing closing brace for input object
      });
      // Clear pending relationship changes after successful save
      setRelationshipsToAdd([]);
      setRelationshipsToRemove([]);
      // Autosave indicator is in TiptapEditor, no need for toast here unless desired
    } catch (error) {
      console.error("Failed to save article:", error);
      toast({ title: "Error saving article", description: `Could not save changes. ${error instanceof Error ? error.message : ''}`, variant: "destructive" });
    }
  // Include relationship states in dependencies
  }, [fileId, title, updateArticle, toast, relationshipsToAdd, relationshipsToRemove]); 

  // Handle Delete
  const handleDelete = async () => {
    // TODO: Add confirmation dialog
    try {
      await deleteArticleMutation({ fileId });
      toast({ title: "Success", description: "Article deleted." });
      router.push('/documents'); // Navigate back to documents list after delete
    } catch (error) {
      console.error("Failed to delete article:", error);
      toast({ title: "Error", description: "Failed to delete article.", variant: "destructive" });
    }
  };


  // Loading and Error States
  if (!isLoaded && articleData === undefined) { 
    return <div className="p-6">Loading article...</div>;
  }

  // Handle case where articleData is explicitly null after loading
  if (articleData === null && isLoaded) {
    return <div className="text-red-600 p-6">Error: Article not found or not a Knowledge Base article.</div>;
  }

  // Render the page content only when loaded
  return (
    <div className="p-6 max-w-4xl mx-auto">
      {/* Editable title input */}
      <Input
        value={title}
        onChange={(e) => setTitle(e.target.value)}
        placeholder="Article Title"
        className="text-3xl font-bold mb-2 border-none focus-visible:ring-0 shadow-none p-0 h-auto" // Adjusted margin-bottom to mb-2
        onBlur={() => {
          // Save only if loaded, title changed from initial, and is not empty
          const trimmedTitle = title.trim();
          if (isLoaded && initialTitle !== null && trimmedTitle !== initialTitle && trimmedTitle !== '') {
            updateArticle({ input: { fileId: fileId, title: trimmedTitle } })
              .then(() => {
                setInitialTitle(trimmedTitle); // Update initial title after successful save
                // toast({ title: "Title Saved", description: "Article title updated." }); // Optional toast
              })
              .catch((error) => {
                console.error("Failed to save title:", error);
                toast({ title: "Error saving title", description: `Could not save title. ${error instanceof Error ? error.message : ''}`, variant: "destructive" });
                // Optionally revert title state on error
                // setTitle(initialTitle ?? '');
              });
          } else if (trimmedTitle === '' && initialTitle !== '') {
             // If user cleared the title, revert to initial title on blur
             setTitle(initialTitle ?? '');
          }
        }}
      />

      {/* Display the short description if available */}
      {isLoaded && articleData && (
        <DocumentSummaryDisplay summary={articleData.short_description} />
      )}

      {/* Use the existing TiptapEditor component */}
      {isLoaded && ( // Render editor only when initial content is ready
        <TiptapEditor
          key={fileId} // Use key to force re-mount if fileId changes
          content={currentEditorContent} // Use state variable which is updated by useEffect
          onChange={handleContentChange} 
          onSave={handleSave} 
          variant="full" 
          placeholder="Start writing your knowledge base article..."
          minHeight="300px" 
        />
      )}

      {/* Relationship Manager Component */}
      {isLoaded && ( // Also render manager only when loaded
        <RelationshipManager 
          fileId={fileId} 
          onRelationshipsChange={handleRelationshipsChange} 
        />
      )}

      {/* Buttons */}
      <div className="mt-6 flex justify-end space-x-2">
        <Button variant="outline" onClick={() => router.back()}> {/* Navigate back */}
          Close 
        </Button>
        <Button variant="destructive" onClick={handleDelete}> {/* Added Delete button */}
          Delete
        </Button>
        {/* Explicit Save button removed, relying on TiptapEditor's autosave + title blur */}
      </div>
    </div>
  );
};

export default KnowledgeBaseArticlePage;
