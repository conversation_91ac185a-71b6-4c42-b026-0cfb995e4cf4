'use client';

import React, { use, useState, useEffect, useCallback, useRef } from 'react';
import { use<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import { useQuery, useMutation } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useToast } from '@/components/hooks/use-toast';
import { TiptapEditor } from '@/components/tiptap/TiptapEditor';
import { RealtimeTranscriptEditor } from '@/components/documents/RealtimeTranscriptEditor';
import MeetingNoteActionBar from '@/components/documents/MeetingNoteActionBar'; // Import the new action bar
import MeetingDetails from '@/components/documents/MeetingDetails'; // Import the meeting details component
import DocumentSummaryDisplay from '@/components/documents/DocumentSummaryDisplay';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'; // Added CardContent, CardHeader, CardTitle
import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from '@/components/ui/collapsible';
import { ChevronDown, FileText, Info, Mic, Pencil, Sparkles } from 'lucide-react'; // Added Info, Pencil
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover'; // Added Popover
import CollectionCardModal from '@/components/ui/collectionCardModal'; // Added Modal

// Helper functions
const isHtmlContentEmpty = (html: string | null | undefined): boolean => {
  if (!html) return true;
  const text = html.replace(/<[^>]*>/g, '').trim();
  return text.length === 0;
};

const getTranscriptPreview = (html: string | null | undefined, maxLength: number = 100): string => {
  if (!html || isHtmlContentEmpty(html)) return "No transcript available";
  const text = html.replace(/<[^>]*>/g, '').replace(/\s+/g, ' ').trim();
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text;
};

const MeetingNotePage = ({ params }: { params: Promise<{ id: Id<'files'> }> }) => {
  const [noteMode, setNoteMode] = useState<'aiAssisted' | 'manual'>('aiAssisted');
  const [isRecording, setIsRecording] = useState(false);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isGeneratingNotes, setIsGeneratingNotes] = useState(false);
  const realtimeEditorRef = useRef<any>(null); // Using any to avoid type conflicts with RealtimeTranscriptEditor
  const unwrappedParams = use(params);
  const router = useRouter();
  const { toast } = useToast();
  const fileId = unwrappedParams.id;

  const [title, setTitle] = useState('');
  const [aiNotesContent, setAiNotesContent] = useState<string>('');
  const [manualNotesContent, setManualNotesContent] = useState<string>('');
  const [transcriptContent, setTranscriptContent] = useState<string>('');
  const [showAiEmptyState, setShowAiEmptyState] = useState(false);
  const [showEditTranscriptWarning, setShowEditTranscriptWarning] = useState(false); // State for warning modal
  const [isTranscriptEditable, setIsTranscriptEditable] = useState(false); // State to control transcript editability
  const [isLoaded, setIsLoaded] = useState(false);
  const [isTranscriptExpanded, setIsTranscriptExpanded] = useState(true);
  const [initialTitle, setInitialTitle] = useState<string | null>(null); // Store initial title
  const [initialLoad, setInitialLoad] = useState(true);
  // Removed unused contentChanged state

  const meetingNoteData = useQuery(api.files.meetingNoteQueries.getMeetingNote, { fileId });
  const updateMeetingNote = useMutation(api.files.meetingNoteMutations.updateMeetingNote);
  const deleteMeetingNote = useMutation(api.files.meetingNoteMutations.deleteMeetingNote);
  const removeTagMutation = useMutation(api.tags.removeTagFromTaggable); // Add mutation for removing tags

  // Fetch tags for this meeting note (file)
  const tags = useQuery(api.tags.getTagsForTaggable, { taggable_id: fileId, taggable_type: 'file' }); // Corrected prop names

  useEffect(() => {
    if (meetingNoteData) {
      const loadedTitle = meetingNoteData.title ?? '';

      // --- Robust Title Update Logic ---
      // Only update title state if:
      // 1. It's the very first load (initialTitle is null)
      // 2. OR the user hasn't made local edits (title === initialTitle) AND the loaded title differs
      if (initialTitle === null) {
        setTitle(loadedTitle);
        setInitialTitle(loadedTitle); // Set initial title on first load
      } else if (title === initialTitle && loadedTitle !== title) {
        // Update if no local edits and fetched title is different
        setTitle(loadedTitle);
        setInitialTitle(loadedTitle); // Keep initialTitle in sync if updated externally
      }
      // --- End Robust Title Update Logic ---

      // Update content fields (only if changed to avoid unnecessary re-renders)
      const aiContentString = meetingNoteData.mnContent as string | null | undefined;
      if (aiContentString !== aiNotesContent) {
        console.log('Received AI content update from Convex:',
          aiContentString?.substring(0, 40) + (aiContentString && aiContentString.length > 40 ? '...' : ''));
        setAiNotesContent(aiContentString ?? '');
      }
      const manualContentString = meetingNoteData.mnManualNotes as string | null | undefined;
      if (manualContentString !== manualNotesContent) {
        setManualNotesContent(manualContentString ?? '');
      }
      const transcriptString = meetingNoteData.mnTranscript as string | null | undefined;
      if (transcriptString !== transcriptContent) {
        setTranscriptContent(transcriptString ?? '');
      }

      // Check if we should default to manual notes tab
      // Only do this on initial load
      if (!isLoaded) {
        // If both AI and manual notes exist, and manual notes have length > 20, show manual tab by default
        if (!isHtmlContentEmpty(aiContentString) && !isHtmlContentEmpty(manualContentString) && 
            manualContentString && manualContentString.length > 20) {
          setNoteMode('manual');
        }
        // If AI notes are empty but manual notes exist, show manual tab by default
        else if (isHtmlContentEmpty(aiContentString) && !isHtmlContentEmpty(manualContentString)) {
          setNoteMode('manual');
        }

        setIsLoaded(true);
        // Set initialLoad to false after a short delay to allow state updates to settle
        setTimeout(() => setInitialLoad(false), 100);
      }
    }
    // Dependencies: Include states that determine if an update should happen
  }, [meetingNoteData, isLoaded, initialTitle, title, aiNotesContent, manualNotesContent, transcriptContent]);

  useEffect(() => {
    let timerId: ReturnType<typeof setTimeout> | null = null;
    if (noteMode === 'aiAssisted' && isHtmlContentEmpty(aiNotesContent) && !isRecording) {
      timerId = setTimeout(() => setShowAiEmptyState(true), 1200);
    } else {
      setShowAiEmptyState(false);
    }
    return () => { if (timerId) clearTimeout(timerId); };
  }, [noteMode, aiNotesContent, isRecording]);

  // Removed unused handleContentChange function

  // Separate function for AI editor saves (with built-in debouncing)
  const handleAiEditorSave = async (content: string) => {
    if (initialLoad) return Promise.resolve();

    try {
      const titleToSave = title || "Untitled Meeting Note";
      await updateMeetingNote({
        fileId: fileId,
        title: titleToSave,
        content: content, // Only update AI content field
        transcript: transcriptContent
      });
      return Promise.resolve();
    } catch (error) {
      console.error("Failed to save AI notes:", error);
      toast({
        title: "Error saving AI notes",
        description: `Could not save changes. ${error instanceof Error ? error.message : ''}`,
        variant: "destructive"
      });
      throw error;
    }
  };

  // Separate function for Manual editor saves (with built-in debouncing)
  const handleManualEditorSave = async (content: string) => {
    if (initialLoad) return Promise.resolve();

    try {
      const titleToSave = title || "Untitled Meeting Note";
      await updateMeetingNote({
        fileId: fileId,
        title: titleToSave,
        manualNotes: content, // Only update Manual notes field
        transcript: transcriptContent
      });
      return Promise.resolve();
    } catch (error) {
      console.error("Failed to save manual notes:", error);
      toast({
        title: "Error saving manual notes",
        description: `Could not save changes. ${error instanceof Error ? error.message : ''}`,
        variant: "destructive"
      });
      throw error;
    }
  };

  // Removed unused handleTranscriptChange function

  const handleTranscriptSave = useCallback(async (transcriptToSave: string) => {
    if (initialLoad) return;

    setTranscriptContent(transcriptToSave);
    try {
      await updateMeetingNote({
        fileId: fileId,
        transcript: transcriptToSave
      });
    } catch (error) {
      console.error("Failed to save transcript:", error);
      toast({
        title: "Error saving transcript",
        description: `Could not save changes. ${error instanceof Error ? error.message : ''}`,
        variant: "destructive"
      });
    }
  }, [fileId, updateMeetingNote, toast, initialLoad]);

  // Handle confirming transcript edit after warning
  const handleConfirmEditTranscript = useCallback(async () => {
    setShowEditTranscriptWarning(false); // Close modal
    setAiNotesContent(''); // Clear AI notes state immediately
    setIsTranscriptEditable(true); // Enable transcript editing

    // Save the cleared AI notes and current transcript to the database
    try {
      await updateMeetingNote({
        fileId: fileId,
        content: '', // Explicitly save empty content
        transcript: transcriptContent // Save current transcript state
      });
      toast({ title: "AI Notes Cleared", description: "You can now edit the transcript. AI notes will regenerate after saving." });
    } catch (error) {
      console.error("Failed to clear AI notes before transcript edit:", error);
      toast({ title: "Error", description: "Could not clear AI notes.", variant: "destructive" });
      // Revert editability if save fails? Or allow edit anyway? For now, allow edit.
      // setIsTranscriptEditable(false);
    }
  }, [fileId, updateMeetingNote, toast, transcriptContent]); // Added transcriptContent

  const handleDelete = async () => {
    // TODO: Add confirmation dialog
    try {
      await deleteMeetingNote({ fileId });
      toast({ title: "Success", description: "Meeting Note deleted." });
      router.push('/documents');
    } catch (error) {
      console.error("Failed to delete meeting note:", error);
      toast({ title: "Error", description: "Failed to delete meeting note.", variant: "destructive" });
    }
  };

  // Handler for removing tags
  const handleRemoveTag = async (tagId: Id<'tags'>) => {
    try {
      await removeTagMutation({ taggable_id: fileId, taggable_type: 'file', tagId }); // Corrected prop names
      toast({ title: "Tag removed" });
    } catch (error) {
      console.error("Failed to remove tag:", error);
      toast({ title: "Error removing tag", variant: "destructive" });
    }
  };

  if (!isLoaded && meetingNoteData === undefined) return <div className="p-6">Loading meeting note...</div>;
  if (meetingNoteData === null && isLoaded) return <div className="text-red-600 p-6">Error: Meeting Note not found.</div>;

  // Removed unused formattedMeetingDate

  return (
    <div className="p-1 sm:p-2 md:p-6 max-w-full"> {/* Reduced padding and added max-width control */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-2 lg:gap-6">
        {/* Main Content Column */}
        <div className="lg:col-span-2 space-y-3 lg:space-y-6 min-w-0"> {/* Added min-w-0 to prevent overflow */}
          {/* Editable title input */}
          <Input
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            placeholder="Meeting Note Title"
            className="text-xl sm:text-2xl md:text-3xl font-bold mb-1 border-none ring-0 focus-visible:ring-2 focus-visible:ring-blue-500 shadow-none p-0 h-auto w-full bg-transparent"
            onBlur={() => {
              // Save only if loaded, title changed from initial, and is not empty
              const trimmedTitle = title.trim();
              if (isLoaded && initialTitle !== null && trimmedTitle !== initialTitle && trimmedTitle !== '') {
                updateMeetingNote({ fileId: fileId, title: trimmedTitle })
                  .then(() => {
                    setInitialTitle(trimmedTitle); // Update initial title after successful save
                    // toast({ title: "Title Saved", description: "Meeting note title updated." }); // Optional toast
                  })
                  .catch((error) => {
                    console.error("Failed to save title:", error);
                    toast({ title: "Error saving title", description: `Could not save title. ${error instanceof Error ? error.message : ''}`, variant: "destructive" });
                    // Optionally revert title state on error
                    // setTitle(initialTitle ?? '');
                  });
              } else if (trimmedTitle === '' && initialTitle !== '') {
                 // If user cleared the title, revert to initial title on blur
                 setTitle(initialTitle ?? '');
              }
            }}
          />

          {/* Display short description if available */}
          {isLoaded && meetingNoteData && meetingNoteData.short_description && meetingNoteData.short_description !== 'No Description' && (
            <DocumentSummaryDisplay summary={meetingNoteData.short_description} />
          )}

          {/* Render the Action Bar */}
          {isLoaded && meetingNoteData && tags && (
            <MeetingNoteActionBar
              taggableId={fileId}
              tags={tags}
              onRemoveTag={handleRemoveTag}
            />
          )}

          {/* Mode switcher and recording button */}
          <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-4 my-2 sm:my-4">
            <div className="flex items-center gap-1 sm:gap-2 overflow-x-auto">
              <span className="text-sm font-medium whitespace-nowrap">Note Mode:</span>
              <Popover>
                <PopoverTrigger asChild>
                  <Info className="h-4 w-4 text-muted-foreground cursor-pointer hover:text-foreground" />
                </PopoverTrigger>
                <PopoverContent className="text-sm w-[280px] sm:w-80">
                  <h4 className="font-medium mb-2">Note Modes Explained</h4>
                  <p className="mb-2">
                    <strong className="text-foreground">Manual Mode:</strong> Take notes yourself. These notes are never overwritten by the AI and can be edited even during live recording.
                  </p>
                  <p>
                    <strong className="text-foreground">AI Assisted Mode:</strong> Notes are automatically generated by AI based on the transcript. You can edit these notes *after* the recording is complete.
                  </p>
                </PopoverContent>
              </Popover>
              <Tabs
                value={noteMode}
                onValueChange={(value) => setNoteMode(value as 'aiAssisted' | 'manual')}
                className="w-auto ml-2"
              >
                <TabsList className="grid w-full grid-cols-2">
                  <TabsTrigger value="aiAssisted" className="flex items-center gap-1 px-2 sm:px-4">
                    <Sparkles className="h-3.5 w-3.5" />
                    <span className="hidden sm:inline">AI Assisted</span>
                    <span className="sm:hidden">AI</span>
                  </TabsTrigger>
                  <TabsTrigger value="manual">
                    Manual
                  </TabsTrigger>
                </TabsList>
              </Tabs>
            </div>

            <Button
              className="flex items-center gap-1 h-8 w-full sm:w-auto justify-center"
              onClick={() => {
                if (isRecording && realtimeEditorRef.current?.stopRecording) {
                  realtimeEditorRef.current.stopRecording();
                } else if (!isRecording && realtimeEditorRef.current?.startRecording) {
                  realtimeEditorRef.current.startRecording();
                }
              }}
              variant={isRecording ? "destructive" : "default"}
              disabled={isConnecting}
            >
              {isConnecting ? (
                <><span className="h-4 w-4 animate-spin mr-1">⏳</span><span className="font-normal">Connecting...</span></>
              ) : isRecording ? (
                <><Mic className="h-4 w-4" /><span className="font-normal">Stop Recording</span></>
              ) : (
                <><Mic className="h-4 w-4" /><span className="font-normal">Start Recording</span></>
              )}
              <Badge variant="secondary" className="ml-1 bg-black/10 text-xs">BETA</Badge>
            </Button>
          </div>

          {/* Main Editor Area */}
          {isLoaded && (
            <div className="mt-4">
              {noteMode === 'aiAssisted' && showAiEmptyState && !isRecording && !isGeneratingNotes ? (
                <div className="border-2 border-gray-300 rounded-md p-8 flex flex-col items-center justify-center text-center min-h-[300px]">
                  <Sparkles className="h-10 w-10 text-blue-400 mb-4" />
                  <h3 className="text-xl font-medium mb-2">AI-Assisted Notes Ready</h3>
                  <p className="text-muted-foreground max-w-md mb-6">
                    Start a recording or upload a transcript to generate AI-assisted meeting notes
                  </p>
                  <Button
                    className="flex items-center gap-1"
                    onClick={() => {
                      if (!isRecording && realtimeEditorRef.current?.startRecording) {
                        realtimeEditorRef.current.startRecording();
                      }
                    }}
                    variant="default"
                    disabled={isConnecting || isRecording}
                  >
                    {isConnecting ? (
                      <><span className="h-4 w-4 animate-spin mr-1">⏳</span>Connecting...</>
                    ) : (
                      <><Mic className="h-4 w-4" />Start Recording</>
                    )}
                    <Badge variant="secondary" className="ml-1 bg-black/10 text-xs">BETA</Badge>
                  </Button>
                </div>
              ) :
              (noteMode === 'aiAssisted' && isRecording && isHtmlContentEmpty(aiNotesContent)) ||
              (noteMode === 'aiAssisted' && isGeneratingNotes) ? (
                <div className="border-2 border-gray-300 rounded-md p-8 flex flex-col items-center justify-center text-center min-h-[300px]">
                  <div className="flex items-center mb-4">
                    <Sparkles className="h-8 w-8 text-blue-400 mr-3" />
                    <span className="h-3 w-3 bg-red-500 rounded-full animate-pulse"></span>
                  </div>
                  <h3 className="text-xl font-medium mb-2">
                    {isRecording ? "Generating AI Notes" : "Processing Transcript"}
                  </h3>
                  <p className="text-muted-foreground max-w-md mb-2">
                    {isRecording
                      ? "AI is automatically generating notes from your transcript as you speak"
                      : "AI is finalizing your meeting notes from the full transcript"}
                  </p>
                  <p className="text-sm text-blue-600 mb-6">
                    {isRecording
                      ? "Notes will update shortly after each pause in the conversation"
                      : "Your notes will appear in a few moments"}
                  </p>
                  <div className="w-full max-w-md h-1 bg-gray-200 rounded-full overflow-hidden">
                    <div className="h-full bg-blue-400 rounded-full animate-pulse" style={{ width: '100%' }}></div>
                  </div>
                </div>
              ) : (
                <div className="w-full">
                  {/* AI Notes Editor - Only shown in AI Assisted mode */}
                  {noteMode === 'aiAssisted' && (
                    <TiptapEditor
                      key={`meeting-notes-ai-editor-${fileId}`}
                      content={aiNotesContent}
                      onChange={() => {}}
                      onSave={handleAiEditorSave}
                      variant="full"
                      placeholder="AI-assisted notes will appear here..."
                      minHeight="300px"
                      isEditable={!isRecording && !isGeneratingNotes}
                    />
                  )}

                  {/* Manual Notes Editor - Only shown in Manual mode */}
                  {noteMode === 'manual' && (
                    <TiptapEditor
                      key={`meeting-notes-manual-editor-${fileId}`}
                      content={manualNotesContent}
                      onChange={() => {}}
                      onSave={handleManualEditorSave}
                      variant="full"
                      placeholder="Start writing your manual meeting notes..."
                      minHeight="300px"
                      isEditable={true}
                    />
                  )}
                </div>
              )}
            </div>
          )}

          {/* Transcript Section */}
          {isLoaded && (
            <div className="mt-3 lg:mt-6">
              <Collapsible
                className="w-full"
                open={isTranscriptExpanded}
                onOpenChange={setIsTranscriptExpanded}
              >
                <div className="mb-2">
                  <CollapsibleTrigger className="flex flex-col sm:flex-row items-start sm:items-center justify-between w-full p-2 sm:p-3 md:p-4 font-medium text-left bg-muted hover:bg-muted/80 rounded-md border border-muted">
                    <div className="flex items-center gap-2 mb-2 sm:mb-0">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <span>Transcript</span>
                    </div>

                    {!isTranscriptExpanded && (
                      <div className="flex-1 mx-0 sm:mx-4 text-muted-foreground text-sm overflow-hidden w-full sm:w-auto">
                        {isHtmlContentEmpty(transcriptContent) ? (
                          <div className="flex items-center">
                            <FileText className="h-5 w-5 mr-2 opacity-50" />
                            <span className="text-blue-600">Add Transcript</span>
                          </div>
                        ) : (
                          <span className="line-clamp-1">{getTranscriptPreview(transcriptContent)}</span>
                        )}
                      </div>
                    )}

                    <div className="flex items-center gap-2 mt-2 sm:mt-0">
                      {/* Only show Add Transcript button when collapsed AND transcript is NOT empty */}
                      {!isTranscriptExpanded && !isHtmlContentEmpty(transcriptContent) && (
                        <span className="inline-flex items-center gap-1 text-sm font-medium text-blue-600 hover:text-blue-700 hover:bg-blue-50 px-2 py-1 rounded-md cursor-pointer">
                          <FileText className="h-4 w-4" />
                          Add Transcript
                        </span>
                      )}
                      {isTranscriptExpanded && (
                        <>
                          <span
                            className="inline-flex items-center gap-1 text-xs font-medium bg-background hover:bg-muted px-2 py-1 rounded-md cursor-pointer h-7"
                            onClick={(e) => {
                              e.stopPropagation(); // Prevent triggering the collapsible
                              if (isRecording && realtimeEditorRef.current?.stopRecording) {
                                realtimeEditorRef.current.stopRecording();
                              } else if (!isRecording && realtimeEditorRef.current?.startRecording) {
                                realtimeEditorRef.current.startRecording();
                              }
                            }}
                          >
                            {isConnecting ? (
                              <span className="h-3 w-3 animate-spin mr-1">⏳</span>
                            ) : isRecording ? (
                              <Mic className="h-3 w-3 text-red-500" />
                            ) : (
                              <Mic className="h-3 w-3" />
                            )}
                            <span className="font-normal">
                              {isConnecting ? "Connecting..." : isRecording ? "Stop" : "Record"}
                            </span>
                          </span>

                          {/* Edit/Lock Transcript Button - Show if not recording and transcript exists */}
                          {!isRecording && !isHtmlContentEmpty(transcriptContent) && (
                            <span
                              className={`inline-flex items-center gap-1 text-xs font-medium px-2 py-1 rounded-md cursor-pointer h-7 ml-2 ${
                                isTranscriptEditable 
                                  ? "bg-blue-50 text-blue-600 hover:bg-blue-100" 
                                  : "bg-background hover:bg-muted"
                              }`}
                              onClick={(e) => {
                                e.stopPropagation(); // Prevent triggering the collapsible
                                if (isTranscriptEditable) {
                                  // If currently editable, lock it down
                                  setIsTranscriptEditable(false);
                                } else {
                                  // If currently locked, show warning before enabling edit
                                  setShowEditTranscriptWarning(true);
                                }
                              }}
                            >
                              {isTranscriptEditable ? (
                                <>
                                  <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-lock">
                                    <rect width="18" height="11" x="3" y="11" rx="2" ry="2"/>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
                                  </svg>
                                  <span>Lock</span>
                                </>
                              ) : (
                                <>
                                  <Pencil className="h-3 w-3" />
                                  <span>Edit</span>
                                </>
                              )}
                            </span>
                          )}
                        </>
                      )}
                      <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isTranscriptExpanded ? 'rotate-180' : ''}`} />
                    </div>
                  </CollapsibleTrigger>
                </div>

                <CollapsibleContent>
                  <Card className="p-2 sm:p-3 md:p-6 shadow-sm">
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
                      <div className="bg-blue-50 rounded-lg p-2.5 border border-blue-100 w-full sm:w-auto sm:flex-1 sm:mr-4">
                        <div className="flex items-start">
                          <div className="bg-blue-100 p-1.5 rounded-full mr-3 mt-0.5 flex-shrink-0">
                            <Mic className="h-4 w-4 text-blue-600" />
                          </div>
                          <div>
                            <p className="text-xs font-medium text-blue-800 mb-1">Recording Instructions</p>
                            <ul className="space-y-1 text-xs text-blue-700">
                              <li><span className="mr-2">•</span>Click the "Start Recording" button to record your meeting in real-time</li>
                              <li className="flex items-start"><span className="text-amber-500 mr-2">⚠</span><span>Do not use headphones as the microphone needs to hear both sides of the conversation</span></li>
                              <li><span className="mr-2">•</span>Or paste an existing transcript into the box below</li>
                            </ul>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="overflow-auto">
                      <RealtimeTranscriptEditor
                        ref={realtimeEditorRef}
                        key={`transcript-${fileId}`}
                        initialContent={transcriptContent}
                        onSave={handleTranscriptSave} // Use specific save handler
                        fileId={fileId}
                        placeholder="Paste transcript here or start recording..."
                        minHeight="200px"
                        maxHeight="400px"
                        hideRecordButton={true} // Button is handled outside
                        onRecordingStateChange={(recording: boolean, connecting: boolean) => {
                          setIsRecording(recording);
                          setIsConnecting(connecting);

                          // If we're stopping recording, show the generating notes state for 7 seconds
                          // This gives time for the AI to process the transcript and generate notes
                          if (!recording && isRecording) {
                            setIsGeneratingNotes(true);
                            setTimeout(() => {
                              setIsGeneratingNotes(false);
                            }, 7000); // Wait 7 seconds for notes to generate
                          }
                        }}
                        isEditable={isTranscriptEditable || isHtmlContentEmpty(transcriptContent)} // Make editable when empty
                      />
                    </div>

                    <p className="text-sm text-muted-foreground mt-2">
                      AI will automatically generate notes from your transcript
                    </p>
                  </Card>
                </CollapsibleContent>
              </Collapsible>
            </div>
          )}

          {/* Buttons */}
          <div className="mt-3 lg:mt-6 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4">
            <div>
              {noteMode === 'aiAssisted' && (
                <Badge variant="outline" className="bg-blue-50 text-blue-600 border-blue-200">
                  <Sparkles className="h-3 w-3 mr-1" />
                  AI Mode Active
                </Badge>
              )}
            </div>
            <div className="flex space-x-2 w-full sm:w-auto">
              <Button variant="outline" onClick={() => router.back()} className="flex-1 sm:flex-initial">
                Close
              </Button>
              <Button variant="destructive" onClick={handleDelete} className="flex-1 sm:flex-initial">
                Delete
              </Button>
            </div>
          </div>
        </div>

        {/* Sidebar Column */}
        <div className="space-y-3 lg:space-y-6 min-w-0"> {/* Added min-w-0 to prevent overflow */}
          {/* Meeting Details Component */}
          {isLoaded && meetingNoteData && (
            <MeetingDetails
              taggableId={fileId}
              meetingDate={meetingNoteData.mnMeetingDate}
              attendeeIds={meetingNoteData.mnAttendees}
            />
          )}
        </div>
      </div>

      {/* Warning Modal for Editing Transcript */}
      {showEditTranscriptWarning && ( // Conditionally render the modal based on state
        <CollectionCardModal
          heading="Edit Transcript?"
          onClose={() => setShowEditTranscriptWarning(false)} // Use onClose to handle the close button
          primaryCTA={{
            text: "Yes, Edit Transcript",
            onClick: handleConfirmEditTranscript,
          }}
          secondaryCTA={{
            text: "Cancel",
            onClick: () => setShowEditTranscriptWarning(false),
          }}
          showFooter={true} // Ensure footer with buttons is shown
        >
          {/* Pass the description as children */}
          <p className="text-sm text-neutral-600">
            Editing the transcript will clear the current AI-generated notes. The AI notes will regenerate based on the new transcript after you save your changes. Are you sure you want to proceed?
          </p>
        </CollectionCardModal>
      )}
    </div>
  );
};

export default MeetingNotePage;
