'use client';

import React, { useState, useEffect } from 'react';
import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';

import Link from 'next/link';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Search } from 'lucide-react';
import { Skeleton } from '@/components/ui/skeleton';
import { MeetingNoteCard } from '@/components/cards/MeetingNoteCard';
import PrimaryChatCTA from '@/components/documents/primary-chat-cta';

export default function MeetingNotesListPage() {
  // State for search and filtering
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');

  // Debounce search input
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearch(searchQuery);
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // State for manual pagination
  const [cursor, setCursor] = useState<string | null>(null);
  const [allMeetingNotes, setAllMeetingNotes] = useState<any[]>([]);

  // Fetch Meeting Notes using the search query and pagination
  const result = useQuery(api.files.meetingNoteQueries.searchMeetingNotes, {
    searchQuery: debouncedSearch,
    pagination: {
      numItems: 12,
      cursor: cursor
    }
  });

  // Update allMeetingNotes when new data is fetched
  useEffect(() => {
    if (result?.meetingNotes) {
      if (cursor) {
        // Append new notes to existing ones
        setAllMeetingNotes(prev => [...prev, ...result.meetingNotes]);
      } else {
        // Initial load
        setAllMeetingNotes(result.meetingNotes);
      }
    }
  }, [result, cursor]);

  // Handle loading more meeting notes
  const handleLoadMore = () => {
    if (result?.continuation) {
      setCursor(result.continuation);
    }
  };

  const isLoading = !result;

  return (
    <div className="p-6">
      {/* Add the Primary Chat CTA component here */}
      <PrimaryChatCTA />

      <div className="mt-8">
        <CardHeader>
          <CardTitle>Meeting Notes</CardTitle>
          <div className="flex items-center gap-2 mt-4">
            <div className="relative flex-1 max-w-sm">
              <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                type="search"
                placeholder="Search meeting notes by title..."
                className="pl-8"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div>
            {/* Loading state with skeleton cards */}
            {isLoading && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {[...Array(6)].map((_, i) => (
                   <Card key={`skel-${i}`} className="overflow-hidden">
                     <CardHeader className="flex flex-row items-start gap-4 space-y-0 pb-3">
                       {/* Use CalendarClock skeleton */}
                       <Skeleton className="h-10 w-10 rounded-md bg-amber-100" />
                       <div className="flex-1 space-y-2">
                         <Skeleton className="h-4 w-3/4" />
                       </div>
                     </CardHeader>
                     <CardContent className="pt-0 pb-3 text-xs text-muted-foreground flex-grow">
                       <Skeleton className="h-3 w-1/2 mt-2" />
                     </CardContent>
                     <div className="border-t px-4 py-2">
                       <Skeleton className="h-3 w-1/3" />
                     </div>
                   </Card>
                ))}
              </div>
            )}

            {/* Grid of Meeting Note cards */}
            {!isLoading && allMeetingNotes.length > 0 && (
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
                {allMeetingNotes.map((note) => (
                  <Link key={note._id} href={`/documents/meeting-notes/${note._id}`} className="block">
                    <MeetingNoteCard meetingNote={note} />
                  </Link>
                ))}
              </div>
            )}

             {/* No results message */}
             {!isLoading && allMeetingNotes.length === 0 && (
               <div className="text-center py-10 text-muted-foreground">
                 No meeting notes found.
               </div>
             )}

            {/* Load More Button */}
            {result?.continuation && (
              <div className="flex justify-center mt-4">
                <Button
                  onClick={handleLoadMore}
                  variant="outline"
                >
                  Load More
                </Button>
              </div>
            )}
          </div>
        </CardContent>
      </div>
    </div>
  );
}
