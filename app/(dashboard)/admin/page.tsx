'use client';

import { useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import {
  Users,
  User,
  Building,
  FileText,
  Tags,
  Plug,
  UsersRound,
} from 'lucide-react';

// Helper component for displaying a statistic card
const StatCard = ({
  title,
  value,
  icon: Icon,
  isLoading,
}: {
  title: string;
  value: number | string | undefined;
  icon: React.ElementType;
  isLoading: boolean;
}) => (
  <Card>
    <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
      <CardTitle className="text-sm font-medium">{title}</CardTitle>
      <Icon className="h-4 w-4 text-muted-foreground" />
    </CardHeader>
    <CardContent>
      {isLoading ? (
        <Skeleton className="h-8 w-1/2" />
      ) : (
        <div className="text-2xl font-bold">{value ?? 'N/A'}</div>
      )}
    </CardContent>
  </Card>
);

export default function AdminPage() {
  // Fetch data using existing list queries
  const usersData = useQuery(api.users.listUsersWithPeopleAndTeams, {}); // Provides user count
  // Use search functions with empty query and explicitly no pagination
  const peopleData = useQuery(api.people.searchPeople, { search: '', paginate: false });
  const orgsData = useQuery(api.organizations.searchOrganizationsByName, { searchQuery: '' });
  // Use getRecentlyModifiedFiles with a large limit to approximate total count
  const filesData = useQuery(api.files.fileStorageQueries.getRecentlyModifiedFiles, { limit: 10000 });
  const tagsData = useQuery(api.tags.fetchTags, { filter: {} }); // Corrected arguments
  const integrationsData = useQuery(
    api.integrations.integrations.listIntegrations,
    {}
  ); // Used in integrations page
  const teamsData = useQuery(api.relationships.teams.listTeams, {}); // Used in teams page

  // Determine loading state
  const isLoading =
    usersData === undefined ||
    peopleData === undefined ||
    orgsData === undefined ||
    filesData === undefined ||
    tagsData === undefined ||
    integrationsData === undefined ||
    teamsData === undefined;

  // Calculate counts
  const totalUsers = usersData?.length;
  // Handle potential PaginationResult type for peopleData even with paginate: false
  const totalPeople = peopleData ? ('page' in peopleData ? peopleData.page.length : peopleData.length) : undefined;
  const totalOrgs = orgsData?.length;
  const totalFiles = filesData?.length;
  const totalTags = tagsData?.length;
  const totalTeams = teamsData?.length;

  // Calculate active integrations count (client-side filter)
  const activeIntegrations = integrationsData?.filter(
    (integration) => integration.status !== 'NEEDS_SETUP'
  ).length;

  return (
    <div className="space-y-6">
      {/* Adjusted grid layout for 7 items */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <StatCard
          title="Total Users"
          value={totalUsers}
          icon={Users}
          isLoading={isLoading}
        />
        <StatCard
          title="Total People"
          value={totalPeople}
          icon={User}
          isLoading={isLoading}
        />
        <StatCard
          title="Total Organizations"
          value={totalOrgs}
          icon={Building}
          isLoading={isLoading}
        />
        <StatCard
          title="Total Documents"
          value={totalFiles}
          icon={FileText}
          isLoading={isLoading}
        />
        <StatCard
          title="Total Tags"
          value={totalTags}
          icon={Tags}
          isLoading={isLoading}
        />
        <StatCard
          title="Active Integrations"
          value={activeIntegrations}
          icon={Plug}
          isLoading={isLoading}
        />
        <StatCard
          title="Total Teams"
          value={totalTeams}
          icon={UsersRound}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
}
