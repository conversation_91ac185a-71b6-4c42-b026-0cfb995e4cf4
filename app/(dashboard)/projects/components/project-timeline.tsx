import React, { useState, useEffect, useRef, useMemo } from "react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
// Removed import from '@/types/timeline'
import { ChevronDown, ChevronUp, Filter, Plus, X, Pen, ClipboardList, Gavel, FolderKanban, Sparkles, Users, Search, MoreHorizontal, Trash2, Check } from "lucide-react"; // Added MoreHorizontal, Trash2, Check
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import ManualUpdateDialog from "./ManualUpdateDialog";
// Removed Droppable import
import { TiptapEditor } from '@/components/tiptap/TiptapEditor';
import { useMutation, useQuery } from 'convex/react';
import { api } from '@/convex/_generated/api';
import { Id } from '@/convex/_generated/dataModel';
import { useDebounce } from '@/components/hooks/use-debounce';
import { useToast } from '@/components/hooks/use-toast';

import { Skeleton } from '@/components/ui/skeleton';
import { Card } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { ScrollArea } from '@/components/ui/scroll-area';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"; // Added DropdownMenu components
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog"; // Added AlertDialog components
// Using standard Popover instead of MorphingPopover

// Function to strip HTML tags from content
const stripHtmlTags = (html: string) => {
  return html?.replace(/<\/?[^>]+(>|$)/g, "") || "";
};

// Define simplified props, removing types from the deleted file
interface ProjectTimelineProps {
  projectId?: Id<'projects'>; // Add projectId prop
  // Removed items, onDropTask, onAddUpdate props
}

export default function ProjectTimeline({ projectId }: ProjectTimelineProps) { // Removed items prop
  const { toast } = useToast();
  // Removed timeRange state and related types/logic
  // Removed expandedItems state and related logic
  const [searchQuery, setSearchQuery] = useState("");
  const [isManualUpdateDialogOpen, setIsManualUpdateDialogOpen] = useState(false); // Keep this for the dialog
  const [isScrolled, setIsScrolled] = useState(false);
  const [newUpdateId, setNewUpdateId] = useState<Id<'projectUpdates'> | null>(null); // Track newly created empty update
  const [editingUpdateId, setEditingUpdateId] = useState<Id<'projectUpdates'> | null>(null); // Track if we're editing an existing update
  const [refreshTrigger, setRefreshTrigger] = useState(0); // Used to trigger a refresh of the updates
  const [expandedUpdateIds, setExpandedUpdateIds] = useState<Set<string>>(new Set()); // Track expanded updates
  const [datePickerOpen, setDatePickerOpen] = useState<Id<'projectUpdates'> | null>(null); // Track which update's date picker is open
  const [selectedDate, setSelectedDate] = useState<Date | undefined>(undefined); // Track selected date
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false); // State for delete confirmation dialog
  const [updateToDelete, setUpdateToDelete] = useState<Id<'projectUpdates'> | null>(null); // State to store the ID of the update to delete
  const [isFilterPopoverOpen, setIsFilterPopoverOpen] = useState(false); // State for filter popover (not needed with standard Popover but keeping for consistency)
  const [selectedFilters, setSelectedFilters] = useState<Set<string>>(new Set(['MANUAL', 'MEETING'])); // Track selected filter types, both selected by default

  // Add Update Popover state
  const [isAddUpdatePopoverOpen, setIsAddUpdatePopoverOpen] = useState(false);
  const [popoverStep, setPopoverStep] = useState<'selectType' | 'searchMeeting'>('selectType');
  const [meetingSearchTerm, setMeetingSearchTerm] = useState("");
  const [debouncedMeetingSearchTerm] = useDebounce(meetingSearchTerm, 300);
  const [isCreatingLink, setIsCreatingLink] = useState(false);

  // Convex mutations
  const createManualUpdate = useMutation(api.projectUpdates.createManualUpdate);
  const createMeetingLinkUpdate = useMutation(api.projectUpdates.createMeetingLinkUpdate);
  const updateProjectUpdateContent = useMutation(api.projectUpdates.updateProjectUpdateContent);
  const updateProjectUpdateDate = useMutation(api.projectUpdates.updateProjectUpdateDate); // Using dedicated mutation for date updates
  const deleteProjectUpdate = useMutation(api.projectUpdates.deleteProjectUpdate); // Added delete mutation

  // Meeting notes search query
  const meetingNotes = useQuery(
    api.files.meetingNoteQueries.searchMeetingNotes,
    debouncedMeetingSearchTerm ? {
      searchQuery: debouncedMeetingSearchTerm,
      pagination: {
        numItems: 10,
        cursor: null
      }
    } : 'skip'
  );

  // Fetch project updates if projectId is available
  const projectUpdates = useQuery(
    api.projectUpdates.listByProject,
    projectId ? {
      input: {
        project_id: projectId,
        limit: 10
      }
    } : 'skip'
  );

  // Reset popover state when it closes
  useEffect(() => {
    if (!isAddUpdatePopoverOpen) {
      setPopoverStep('selectType');
      setMeetingSearchTerm('');
    }
  }, [isAddUpdatePopoverOpen]);

  // Effect to refetch updates when refreshTrigger changes
  useEffect(() => {
    if (refreshTrigger > 0) {
      // This will cause the query to refetch
      // The actual implementation depends on how your app handles refetching
      // This is a placeholder
    }
  }, [refreshTrigger]);

  // Fetch people data for user avatars and names
  const people = useQuery(api.directory.directoryPeople.listPeople, {
    sortBy: 'name',
    sortDirection: 'asc',
    limit: 100
  });

  // Function to get user info by ID
  const getUserInfo = (userId: Id<'users'>) => {
    // Find the person record linked to this user
    const person = people?.people.find(p => p.user_id === userId);

    if (person) {
      return {
        name: person.name || 'Unknown User',
        image: person.image || '',
        initials: person.name ? `${person.name.charAt(0)}${person.name.includes(' ') ? person.name.split(' ')[1].charAt(0) : ''}` : 'UN'
      };
    }

    // Fallback if person not found
    return {
      name: 'User ' + userId.slice(-4),
      image: '',
      initials: 'UN'
    };
  };

  // Function to toggle expanded state of an update
  const toggleExpandUpdate = (updateId: string) => {
    setExpandedUpdateIds(prev => {
      const newSet = new Set(prev);
      if (newSet.has(updateId)) {
        newSet.delete(updateId);
      } else {
        newSet.add(updateId);
      }
      return newSet;
    });
  };

  // For now, we'll just use the content field for everything
  // In a real implementation, we would have separate mutations for date and title

  const sentinelRef = useRef<HTMLDivElement>(null);
  const stickyHeaderRef = useRef<HTMLDivElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Setup Intersection Observer for scroll detection
  useEffect(() => {
    if (!sentinelRef.current) return;
    const observer = new IntersectionObserver(
      ([entry]) => setIsScrolled(!entry.isIntersecting),
      { root: null, rootMargin: '0px', threshold: 0 }
    );
    observer.observe(sentinelRef.current);
    return () => observer.disconnect();
  }, []);

  // Removed filterItemsByTimeRange function and related logic

  // Handle saving content to Convex
  const handleSaveContent = async (htmlContent: string) => {
    if (!projectId) {
      console.error('Project ID is required to save updates');
      return Promise.reject(new Error('Project ID is required'));
    }

    try {
      if (editingUpdateId) {
        // Update existing project update
        await updateProjectUpdateContent({
          input: {
            update_id: editingUpdateId,
            content: htmlContent
          }
        });
      } else {
        // Create new project update
        const result = await createManualUpdate({
          input: {
            project_id: projectId,
            content: htmlContent,
            date_of_update: Date.now()
          }
        });

        // Store the ID for future edits
        setEditingUpdateId(result.id as Id<'projectUpdates'>);
      }

      // Trigger a refresh of the updates
      setRefreshTrigger(prev => prev + 1);
      return Promise.resolve();
    } catch (error) {
      console.error('Error saving project update:', error);
      return Promise.reject(error);
    }
  };

  // Create a new empty update
  const createEmptyUpdate = async () => {
    if (!projectId) {
      console.error('Project ID is required');
      toast({ title: "Error", description: "Project ID missing.", variant: "destructive" });
      return;
    }

    try {
      // Create a new update with empty content
      const result = await createManualUpdate({
        input: {
          project_id: projectId,
          content: '<p></p>', // Empty content
          date_of_update: Date.now()
        }
      });

      // Store the ID of the new update
      setNewUpdateId(result.id as Id<'projectUpdates'>);

      // Expand the new update to show the editor
      setExpandedUpdateIds(prev => {
        const newSet = new Set(prev);
        newSet.add(result.id);
        return newSet;
      });

      // Refresh the list to show the new update
      setRefreshTrigger(prev => prev + 1);

      // Return the ID for potential further use
      return result.id;
    } catch (error) {
      console.error('Error creating empty update:', error);
      toast({
        title: "Error",
        description: "Failed to create update. Please try again.",
        variant: "destructive"
      });
      return null;
    }
  };

  // Delete an empty update if user clicks outside
  const deleteEmptyUpdate = async (updateId: Id<'projectUpdates'>) => {
    try {
      await deleteProjectUpdate({ updateId });
      setNewUpdateId(null);
      setRefreshTrigger(prev => prev + 1);
    } catch (error) {
      console.error('Error deleting empty update:', error);
    }
  };

  // Handle opening the delete confirmation dialog
  const handleDeleteClick = (updateId: Id<'projectUpdates'>) => {
    setUpdateToDelete(updateId);
    setIsDeleteDialogOpen(true);
  };

  // Handle confirming the deletion
  const handleConfirmDelete = async () => {
    if (!updateToDelete) return;

    try {
      await deleteProjectUpdate({ updateId: updateToDelete });
      toast({
        title: "Update deleted",
        description: "The project update has been successfully deleted.",
      });
      setRefreshTrigger(prev => prev + 1); // Refresh the list
    } catch (error) {
      console.error('Error deleting project update:', error);
      toast({
        title: "Error",
        description: "Failed to delete project update. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsDeleteDialogOpen(false);
      setUpdateToDelete(null);
    }
  };

  // Handle document-wide click events to detect clicks outside the editor
  useEffect(() => {
    // Only add the listener if we have a new update that might need to be deleted
    if (!newUpdateId) return;

    const handleDocumentClick = (e: MouseEvent) => {
      // Find the update card element
      const updateCard = document.getElementById(`update-card-${newUpdateId}`);
      if (!updateCard) return;

      // Check if the click was outside the card
      if (!updateCard.contains(e.target as Node)) {
        // Get the update from the list
        const update = projectUpdates?.projectUpdates.find(u => u._id === newUpdateId);

        // If the update exists and has empty content, delete it
        if (update && (update.content === '<p></p>' || !update.content)) {
          deleteEmptyUpdate(newUpdateId);
        } else {
          // If content is not empty, just clear the newUpdateId state
          setNewUpdateId(null);
        }
      }
    };

    // Add the click listener
    document.addEventListener('mousedown', handleDocumentClick);

    // Clean up
    return () => {
      document.removeEventListener('mousedown', handleDocumentClick);
    };
  }, [newUpdateId, projectUpdates]);

  // Filter updates based on search query and selected filters
  const filteredUpdates = useMemo(() => {
    if (!projectUpdates?.projectUpdates) {
      return [];
    }

    let filtered = projectUpdates.projectUpdates;

    // Apply search filter if there's a search query
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase().trim();
      filtered = filtered.filter(update => {
        // Get user info for this update
        const userInfo = getUserInfo(update.created_by);

        // Extract title from content if needed
        let title = update.short_description || "";
        if (!title) {
          const contentHtml = update.content;
          const titleMatch = contentHtml.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
          if (titleMatch && titleMatch[1]) {
            title = titleMatch[1].replace(/<\/?[^>]+(>|$)/g, "");
          } else {
            const firstParagraph = contentHtml.match(/<p[^>]*>(.*?)<\/p>/i);
            if (firstParagraph && firstParagraph[1]) {
              title = firstParagraph[1].replace(/<\/?[^>]+(>|$)/g, "");
            }
          }
        }

        // Search in various fields
        return (
          title.toLowerCase().includes(query) ||
          stripHtmlTags(update.content).toLowerCase().includes(query) ||
          (update.update_type || "").toLowerCase().includes(query) ||
          userInfo.name.toLowerCase().includes(query)
        );
      });
    }

    // Apply type filters if any are selected
    if (selectedFilters.size > 0) {
      filtered = filtered.filter(update => selectedFilters.has(update.update_type));
    }

    return filtered;
  }, [projectUpdates?.projectUpdates, searchQuery, people, selectedFilters]);

  // Check if there are no search results
  const noSearchResults = useMemo(() => {
    return searchQuery.trim() && projectUpdates?.projectUpdates &&
      projectUpdates.projectUpdates.length > 0 && filteredUpdates.length === 0;
  }, [searchQuery, projectUpdates?.projectUpdates, filteredUpdates]);

  return (
    <div ref={containerRef} className="glass-panel rounded-xl flex flex-col">
      <div ref={sentinelRef} className="h-0 w-full" />

      {/* Header */}
      <div className="py-4 pb-1">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-xl font-medium">Project Timeline</h2>
          {/* Removed Add Update Popover from header */}
        </div>
      </div>

      {/* Sticky Controls */}
      <div
        ref={stickyHeaderRef}
        className={`sticky top-0 z-10 py-3 backdrop-blur-lg transition-shadow duration-300 ${isScrolled ? 'shadow-[0_1px_3px_0_rgba(0,0,0,0.05)]' : ''}`}
      >
        <div className="flex gap-2">
          <Input
            type="search"
            placeholder="Search updates..."
            className="flex-1"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          {/* Removed TimeRange Select */}
          <Popover open={isFilterPopoverOpen} onOpenChange={setIsFilterPopoverOpen}>
            <PopoverTrigger asChild>
              <Button variant="outline" size="icon" className="h-10 w-10">
                <Filter className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-56 p-0" align="end">
              <div className="p-2">
                <div className="text-sm font-medium px-2 py-1.5 mb-1">Filter by type</div>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-sm px-2 py-1.5 h-auto mb-1"
                  onClick={() => {
                    const newFilters = new Set(selectedFilters);
                    if (newFilters.has('MANUAL')) {
                      newFilters.delete('MANUAL');
                    } else {
                      newFilters.add('MANUAL');
                    }
                    setSelectedFilters(newFilters);
                  }}
                >
                  <div className="w-5 h-5 mr-2 flex items-center justify-center">
                    {selectedFilters.has('MANUAL') ? (
                      <div className="w-4 h-4 rounded-full bg-primary flex items-center justify-center">
                        <Check className="h-3 w-3 text-primary-foreground" />
                      </div>
                    ) : (
                      <div className="w-4 h-4 rounded-full border border-muted-foreground/50" />
                    )}
                  </div>
                  <Pen className="h-4 w-4 mr-2 text-blue-500" />
                  Manual Updates
                </Button>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-sm px-2 py-1.5 h-auto"
                  onClick={() => {
                    const newFilters = new Set(selectedFilters);
                    if (newFilters.has('MEETING')) {
                      newFilters.delete('MEETING');
                    } else {
                      newFilters.add('MEETING');
                    }
                    setSelectedFilters(newFilters);
                  }}
                >
                  <div className="w-5 h-5 mr-2 flex items-center justify-center">
                    {selectedFilters.has('MEETING') ? (
                      <div className="w-4 h-4 rounded-full bg-primary flex items-center justify-center">
                        <Check className="h-3 w-3 text-primary-foreground" />
                      </div>
                    ) : (
                      <div className="w-4 h-4 rounded-full border border-muted-foreground/50" />
                    )}
                  </div>
                  <Users className="h-4 w-4 mr-2 text-orange-500" />
                  Meeting Notes
                </Button>
              </div>
            </PopoverContent>
          </Popover>
          {/* Removed TimeRange Select */}
        </div>
      </div>

      {/* Scrollable Content */}
      <div className="py-4 overflow-y-auto flex-grow animate-fade-in">
        <div className="relative">
          {/* Timeline Connector Line */}
          <div className="timeline-connector" />

          {/* Display Project Updates */}
          <div className="space-y-4">
            {/* Add Update Button (Moved to top) */}
            <Popover open={isAddUpdatePopoverOpen} onOpenChange={setIsAddUpdatePopoverOpen}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  className="w-full h-10 justify-center items-center border-2 border-dashed border-muted-foreground/30 text-muted-foreground hover:bg-muted/50 hover:text-foreground text-sm"
                  data-add-update="true"
                >
                  <Plus className="h-4 w-4 mr-1" /> Add Update
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-72 p-0" align="end">
                {popoverStep === 'selectType' ? (
                  <div className="p-1">
                    <div className="text-sm font-medium px-3 py-2">Select Update Type</div>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-sm px-3 py-2 h-auto"
                      onClick={async () => {
                        setIsAddUpdatePopoverOpen(false); // Close popover
                        await createEmptyUpdate(); // Create a real empty update
                      }}
                    >
                      <Pen className="h-4 w-4 mr-2 text-blue-500" />
                      Manual Update
                    </Button>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-sm px-3 py-2 h-auto"
                      onClick={() => setPopoverStep('searchMeeting')}
                    >
                      <Users className="h-4 w-4 mr-2 text-orange-500" />
                      Meeting Note
                    </Button>
                  </div>
                ) : (
                  <div className="p-3">
                    <div className="flex items-center mb-3">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-8 w-8 p-0 mr-2"
                        onClick={() => setPopoverStep('selectType')}
                      >
                        <ChevronDown className="h-4 w-4 rotate-90" />
                      </Button>
                      <div className="text-sm font-medium">Search Meeting Notes</div>
                    </div>
                    <div className="relative mb-3">
                      <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                      <Input
                        placeholder="Search by title..."
                        className="pl-8"
                        value={meetingSearchTerm}
                        onChange={(e) => setMeetingSearchTerm(e.target.value)}
                      />
                    </div>
                    <ScrollArea className="h-[200px]">
                      {meetingNotes?.meetingNotes?.length === 0 && (
                        <div className="text-sm text-muted-foreground text-center py-4">
                          No meeting notes found
                        </div>
                      )}
                      {!meetingNotes && debouncedMeetingSearchTerm && (
                        <div className="space-y-2">
                          {Array.from({ length: 3 }).map((_, i) => (
                            <div key={i} className="flex items-center p-2 rounded-md">
                              <Skeleton className="h-8 w-8 rounded-full mr-2" />
                              <div className="space-y-1 flex-1">
                                <Skeleton className="h-4 w-3/4" />
                                <Skeleton className="h-3 w-1/2" />
                              </div>
                            </div>
                          ))}
                        </div>
                      )}
                      {meetingNotes?.meetingNotes?.map((note: any) => (
                        <Button
                          key={note._id}
                          variant="ghost"
                          className="w-full justify-start text-sm px-2 py-1.5 h-auto mb-1"
                          disabled={isCreatingLink}
                          onClick={async () => {
                            if (!projectId) return;

                            try {
                              setIsCreatingLink(true);

                              // Call the mutation to create the meeting link update
                              const result = await createMeetingLinkUpdate({
                                projectId: projectId,
                                meetingNoteFileId: note._id
                              });

                              // Show success toast
                              toast({
                                title: "Meeting note linked",
                                description: "The meeting note has been added to the timeline.",
                              });

                              // Close the popover and refresh the timeline
                              setIsAddUpdatePopoverOpen(false);
                              setPopoverStep('selectType');
                              setMeetingSearchTerm("");
                              setRefreshTrigger(prev => prev + 1);
                            } catch (error) {
                              console.error('Error linking meeting note:', error);
                              toast({
                                title: "Error",
                                description: "Failed to link meeting note. Please try again.",
                                variant: "destructive"
                              });
                            } finally {
                              setIsCreatingLink(false);
                            }
                          }}
                        >
                          <Users className="h-4 w-4 mr-2 text-orange-500 flex-shrink-0" />
                          <div className="flex flex-col items-start text-left">
                            <span className="font-medium truncate w-full">{note.title || "Untitled Meeting"}</span>
                            <span className="text-xs text-muted-foreground">
                              {format(new Date(note._creationTime), 'MMM dd, yyyy')}
                            </span>
                          </div>
                        </Button>
                      ))}
                    </ScrollArea>
                  </div>
                )}
              </PopoverContent>
            </Popover>

            {/* Loading state */}
            {!projectUpdates && (
              <div className="space-y-6">
                {Array.from({ length: 2 }).map((_, i) => (
                  <Card key={i} className="overflow-hidden border border-border">
                    <div className="p-4 pb-3">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center gap-1">
                          <Skeleton className="h-4 w-4 rounded" />
                          <Skeleton className="h-4 w-16" />
                        </div>
                        <Skeleton className="h-4 w-20" />
                      </div>
                      <Skeleton className="h-6 w-2/3 mb-3" />
                      <div className="mb-3">
                        <Skeleton className="h-4 w-20 inline-block mr-2" />
                      </div>
                      <div className="space-y-2 mb-3">
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-full" />
                        <Skeleton className="h-4 w-3/4" />
                      </div>
                    </div>
                    <div className="border-t border-border p-3">
                      <div className="flex items-center gap-2">
                        <Skeleton className="h-6 w-6 rounded-full" />
                        <Skeleton className="h-4 w-24" />
                      </div>
                    </div>
                  </Card>
                ))}
              </div>
            )}

            {/* Project updates */}
            {projectUpdates?.projectUpdates && projectUpdates.projectUpdates.length > 0 && (
              <div className="space-y-6">
                {filteredUpdates.map((update) => {
                  const date = new Date(update.date_of_update || update._creationTime);
                  const daysAgo = Math.floor((Date.now() - date.getTime()) / (1000 * 60 * 60 * 24));
                  const dateInfo = {
                    relative: `${daysAgo} days ago`,
                    formatted: format(date, 'MMM dd, yyyy')
                  };

                  // Use short_description if available, otherwise extract from content
                  let title = update.short_description || "Project Update";
                  if (!update.short_description) {
                    const contentHtml = update.content;
                    const titleMatch = contentHtml.match(/<h[1-6][^>]*>(.*?)<\/h[1-6]>/i);
                    if (titleMatch && titleMatch[1]) {
                      // Remove any HTML tags from the title
                      title = titleMatch[1].replace(/<\/?[^>]+(>|$)/g, "");
                    } else {
                      // Try to get the first paragraph or line
                      const firstParagraph = contentHtml.match(/<p[^>]*>(.*?)<\/p>/i);
                      if (firstParagraph && firstParagraph[1]) {
                        const text = firstParagraph[1].replace(/<\/?[^>]+(>|$)/g, "");
                        title = text.length > 50 ? text.substring(0, 50) + "..." : text;
                      }
                    }
                  }

                  // Check if this update is expanded
                  const isExpanded = expandedUpdateIds.has(update._id);

                  // Check if this is the newly created update
                  const isNewUpdate = update._id === newUpdateId;

                  return (
                    <Card
                      id={`update-card-${update._id}`} // Add ID for click outside detection
                      key={update._id}
                      className={`overflow-hidden border hover:shadow-sm transition-all ${
                        isExpanded ? 'shadow-sm' : ''
                      } ${
                        isNewUpdate ? 'border-primary/50 bg-muted/10' : 'border-border'
                      }`}
                    >
                      {/* Collapsed Header - Always visible */}
                      <div className="p-3 cursor-pointer" onClick={() => toggleExpandUpdate(update._id)}>
                        {/* First row: Short description on left, date and chevron on right */}
                        <div className="flex items-center mb-2">
                          <div className="flex items-center gap-2 flex-1 min-w-0 overflow-hidden">
                            {/* Icon based on update type */}
                            {update.update_type === 'MANUAL' && (
                              <Pen className="h-4 w-4 text-blue-500 flex-shrink-0" />
                            )}
                            {update.update_type === 'TASK' && (
                              <ClipboardList className="h-4 w-4 text-green-500 flex-shrink-0" />
                            )}
                            {update.update_type === 'DECISION' && (
                              <Gavel className="h-4 w-4 text-purple-500 flex-shrink-0" />
                            )}
                            {update.update_type === 'MEETING' && (
                              <Users className="h-4 w-4 text-orange-500 flex-shrink-0" />
                            )}
                            {update.update_type === 'AI' && (
                              <Sparkles className="h-4 w-4 text-indigo-500 flex-shrink-0" />
                            )}


                            <h3 className="text-sm font-medium truncate">
                              {update.update_type === 'MEETING' && update.source_id ? (
                                <a
                                  href={`/documents/meeting-notes/${update.source_id}`}
                                  className="hover:underline"
                                  onClick={(e) => e.stopPropagation()} // Prevent card expansion when clicking the link
                                >
                                  {title}
                                </a>
                              ) : (
                                title
                              )}
                            </h3>
                          </div>

                          <div className="flex items-center gap-2 flex-shrink-0 ml-2">
                            <div className="text-xs text-muted-foreground whitespace-nowrap">
                              {dateInfo.relative}
                            </div>
                            <div className="flex-shrink-0">
                              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                            </div>
                          </div>
                        </div>

                        {/* Second row: Badges on left, user on right */}
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-1 flex-wrap">
                            <Badge variant="outline" className="text-xs bg-background whitespace-nowrap">
                              {update.update_type === 'MANUAL' ? 'Manual Update' : update.update_type}
                            </Badge>

                            {/* Display badges if they exist */}
                            {/* Using type assertion since the TypeScript types don't include badges yet */}
                            {(update as any).badges && (update as any).badges.length > 0 && (update as any).badges.map((badgeId: any, index: number) => (
                              <Badge key={index} variant="outline" className="text-xs bg-background whitespace-nowrap">
                                Badge {badgeId.toString().slice(-4)}
                              </Badge>
                            ))}
                          </div>

                          <div className="flex items-center gap-1 flex-shrink-0">
                            <Avatar className="h-5 w-5">
                              <AvatarImage src={getUserInfo(update.created_by).image} />
                              <AvatarFallback className="text-xs">{getUserInfo(update.created_by).initials}</AvatarFallback>
                            </Avatar>
                            <span className="text-xs truncate max-w-[100px]">{getUserInfo(update.created_by).name}</span>
                          </div>
                        </div>
                      </div>

                      {/* Expanded Content - Direct TiptapEditor */}
                      {isExpanded && (
                        <div className="px-4 pb-4">
                          <TiptapEditor
                            content={update.content}
                            onChange={() => {}}
                            onSave={async (htmlContent) => {
                              try {
                                await updateProjectUpdateContent({
                                  input: {
                                    update_id: update._id as Id<'projectUpdates'>,
                                    content: htmlContent
                                  }
                                });

                                // If this was a new update and now has content, clear the newUpdateId state
                                if (isNewUpdate && htmlContent && htmlContent !== '<p></p>') {
                                  setNewUpdateId(null);
                                }

                                setRefreshTrigger(prev => prev + 1);
                                return Promise.resolve();
                              } catch (error) {
                                console.error('Error updating content:', error);
                                return Promise.reject(error);
                              }
                            }}
                            variant="basic"
                            placeholder="Type your update..."
                            minHeight="100px"
                            autoExpand={true}
                            autofocus={isNewUpdate} // Auto focus if this is a new update
                          />

                          {/* Footer with date picker and more options - extremely compact */}
                          <div className="flex justify-end items-center mt-0.5 pt-0.5">
                            {/* Date Picker */}
                            <Popover open={datePickerOpen === update._id} onOpenChange={(open) => {
                              if (open) {
                                setDatePickerOpen(update._id as Id<'projectUpdates'>);
                                setSelectedDate(new Date(update.date_of_update || update._creationTime));
                              } else {
                                setDatePickerOpen(null);
                              }
                            }}>
                              <PopoverTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  className="text-xs text-muted-foreground hover:text-foreground h-6 px-1.5 py-0 -my-0.5"
                                >
                                  {format(new Date(update.date_of_update || update._creationTime), 'MMM dd, yyyy')}
                                </Button>
                              </PopoverTrigger>
                              <PopoverContent className="w-auto p-0" align="end">
                                <Calendar
                                  mode="single"
                                  selected={selectedDate}
                                  onSelect={async (date) => {
                                    if (date) {
                                      setSelectedDate(date);
                                      try {
                                        await updateProjectUpdateDate({
                                          input: {
                                            update_id: update._id as Id<'projectUpdates'>,
                                            date_of_update: date.getTime()
                                          }
                                        });
                                        setRefreshTrigger(prev => prev + 1);
                                        setDatePickerOpen(null);
                                      } catch (error) {
                                        console.error('Error updating date:', error);
                                      }
                                    }
                                  }}
                                  initialFocus
                                />
                              </PopoverContent>
                            </Popover>

                            {/* More Options Dropdown */}
                            <DropdownMenu>
                              <DropdownMenuTrigger asChild>
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  className="h-6 w-6 ml-1 -my-0.5"
                                  onClick={(e) => e.stopPropagation()} // Prevent card expansion
                                >
                                  <MoreHorizontal className="h-4 w-4 text-muted-foreground" />
                                </Button>
                              </DropdownMenuTrigger>
                              <DropdownMenuContent align="end">
                                <DropdownMenuItem
                                  className="text-destructive focus:text-destructive focus:bg-destructive/10"
                                  onSelect={() => handleDeleteClick(update._id as Id<'projectUpdates'>)}
                                >
                                  <Trash2 className="mr-2 h-4 w-4" />
                                  Delete Update
                                </DropdownMenuItem>
                              </DropdownMenuContent>
                            </DropdownMenu>
                          </div>
                        </div>
                      )}
                    </Card>
                  );
                })}
              </div>
            )}

            {/* Empty state - show different message when filtering */}
            {projectUpdates?.projectUpdates && (
              <>
                {projectUpdates.projectUpdates.length === 0 ? (
                  <div className="text-center text-muted-foreground py-8">
                    No updates yet. Add the first one!
                  </div>
                ) : noSearchResults ? (
                  <div className="text-center text-muted-foreground py-8">
                    No updates match your search.
                  </div>
                ) : null}
              </>
            )}
          </div>
        </div>
      </div>

      {/* Manual Update Dialog (triggered by header button) */}
      <ManualUpdateDialog
        isOpen={isManualUpdateDialogOpen}
        onClose={() => setIsManualUpdateDialogOpen(false)}
        onSubmit={(_update) => {
          // Handle manual update from dialog
          setIsManualUpdateDialogOpen(false);
          // If we had the onAddUpdate prop, we would call it here
        }}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the project update.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel onClick={() => setUpdateToDelete(null)}>Cancel</AlertDialogCancel>
            <AlertDialogAction onClick={handleConfirmDelete} className="bg-destructive text-destructive-foreground hover:bg-destructive/90">
              Delete
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
